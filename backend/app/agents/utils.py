from database.core import DbSession
from fastapi import HTTP<PERSON>xception
from google.adk.agents import LlmAgent
from routes.auth.service import CurrentUser
from utils.logger import get_service_logger

from agents.agent_runner import call_agent_async, get_or_create_session, get_runner

# Setup centralized logging
logger = get_service_logger("agents_utils")


async def handle_agent_request(
    db: DbSession, current_user: CurrentUser, query: str, agent: LlmAgent
) -> str:
    APP_NAME = "test_app"
    initial_state = {"user:preferences": {"language": "English"}}
    user_id = current_user.get_uuid()
    if not user_id:
        logger.warning("Unauthorized agent request attempt")
        raise HTTPException(status_code=401, detail="Unauthorized")
    logger.info(f"Processing agent request for user {user_id}")
    session_id = "session_001"  # Using a fixed ID for simplicity
    await get_or_create_session(APP_NAME, str(user_id), session_id, initial_state)
    runner = get_runner(APP_NAME, agent)
    response = await call_agent_async(query, runner, str(user_id), session_id)
    logger.info(f"Agent request processed for user {user_id}")
    return response


def format_agent_event_for_sse(event) -> dict:
    """
    Format a Google ADK agent event for Server-Sent Events (SSE) streaming.

    Args:
        event: Google ADK event object

    Returns:
        dict: Formatted event data suitable for JSON serialization
    """
    event_data = {
        "author": event.author,
        "type": type(event).__name__,
        "is_final": event.is_final_response(),
        "content": None,
        "error_message": getattr(event, "error_message", None),
        "timestamp": None,
    }

    # Extract content if available
    if event.content and event.content.parts:
        event_data["content"] = event.content.parts[0].text
    elif hasattr(event, "actions") and event.actions and event.actions.escalate:
        event_data["content"] = (
            f"Agent escalated: {event.error_message or 'No specific message.'}"
        )

    # Add timestamp if available
    if hasattr(event, "timestamp"):
        event_data["timestamp"] = str(event.timestamp)

    return event_data
