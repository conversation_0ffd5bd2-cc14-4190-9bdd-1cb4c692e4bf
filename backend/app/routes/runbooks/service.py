from datetime import datetime
from enum import Enum
from typing import List
from uuid import UUID

from db_services import runbooks as runbooks_db_service
from entities.incident import Incident
from entities.runbooks import Runbook, RunbookStep, StepStatusEnum
from fastapi import HTTPException, status
from sqlalchemy.orm import Session
from utils.logger import get_service_logger

from routes.agents.service import handle_runbook_agent_request
from routes.auth.service import CurrentUser
from routes.runbooks.models import RunbookCreate, RunbookStepUpdate, RunbookUpdate

logger = get_service_logger("runbooks")


def list_runbooks(db: Session, incident_id: UUID) -> List[Runbook]:
    logger.info(f"Listing runbooks for incident {incident_id}")
    try:
        runbooks = runbooks_db_service.list_runbooks(db, incident_id)
        logger.info(f"Found {len(runbooks)} runbooks for incident {incident_id}")
        return runbooks
    except Exception as e:
        logger.error(f"Failed to list runbooks for incident {incident_id}: {str(e)}")
        raise


def create_runbook(db: Session, incident_id: UUID, payload: RunbookCreate) -> Runbook:
    logger.info(f"Creating runbook for incident {incident_id}")
    try:
        runbook = Runbook(incident_id=incident_id, **payload.model_dump())
        db.add(runbook)
        db.commit()
        db.refresh(runbook)
        logger.info(f"Successfully created runbook with ID: {runbook.id}")
        return runbook
    except Exception as e:
        logger.error(f"Failed to create runbook for incident {incident_id}: {str(e)}")
        raise


def update_runbook(
    db: Session, incident_id: UUID, runbook_id: UUID, payload: RunbookUpdate
):
    logger.info(f"Updating runbook {runbook_id} for incident {incident_id}")
    runbook = (
        db.query(Runbook).filter_by(id=runbook_id, incident_id=incident_id).first()
    )
    if not runbook:
        logger.warning(f"Runbook not found: {runbook_id} for incident {incident_id}")
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND, detail="Runbook not found"
        )

    try:
        for field, value in payload.model_dump().items():
            setattr(runbook, field, value)
        db.commit()
        db.refresh(runbook)
        logger.info(f"Successfully updated runbook: {runbook_id}")
        return runbook
    except Exception as e:
        logger.error(f"Failed to update runbook {runbook_id}: {str(e)}")
        raise


def delete_runbook(db: Session, incident_id: UUID, runbook_id: UUID):
    logger.info(f"Deleting runbook {runbook_id} for incident {incident_id}")
    runbook = (
        db.query(Runbook).filter_by(id=runbook_id, incident_id=incident_id).first()
    )
    if not runbook:
        logger.warning(
            f"Runbook not found for deletion: {runbook_id} for incident {incident_id}"
        )
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND, detail="Runbook not found"
        )

    try:
        db.delete(runbook)
        db.commit()
        logger.info(f"Successfully deleted runbook: {runbook_id}")
    except Exception as e:
        logger.error(f"Failed to delete runbook {runbook_id}: {str(e)}")
        raise


async def generate_steps(
    db: Session, current_user: CurrentUser, incident: Incident, runbook: Runbook
) -> List[RunbookStep]:
    """
    Generate and store new steps for a runbook based on the incident details and existing steps.

    Returns:
        Dictionary containing the newly created step
    """
    logger.info(f"Generating steps for runbook {runbook.id} in incident {incident.id}")

    runbook_steps = runbooks_db_service.get_steps_by_runbook(db, UUID(str(runbook.id)))
    existing_steps = [
        {
            "title": step.title,
            "description": step.description,
            "details": step.details,
            "expected_result": step.expected_result,
            "status": step.status.value
            if isinstance(step.status, Enum)
            else step.status,
            "notes": step.notes,
        }
        for step in runbook_steps
    ]

    logger.info(f"Found {len(existing_steps)} existing steps for runbook {runbook.id}")

    try:
        generated_steps = await handle_runbook_agent_request(
            db, current_user, incident, str(runbook.type), existing_steps
        )
        logger.info(
            f"Agent generated {len(generated_steps)} new steps for runbook {runbook.id}"
        )

        steps_created = []
        for i, step_data in enumerate(generated_steps):
            step = RunbookStep(
                runbook_id=runbook.id,
                step_order=len(existing_steps) + i + 1,
                title=step_data["name"],
                description=step_data["purpose"],
                details=step_data["details"],
                expected_result=step_data["expectedOutcome"],
                status=StepStatusEnum.PENDING,
                created_at=datetime.now(),
            )
            steps_created.append(step)

        db.add_all(steps_created)
        db.commit()
        logger.info(
            f"Successfully created {len(steps_created)} new steps for runbook {runbook.id}"
        )
        return steps_created
    except Exception as e:
        logger.error(f"Failed to generate steps for runbook {runbook.id}: {str(e)}")
        raise


def update_runbook_step(
    db: Session,
    incident_id: UUID,
    runbook_id: UUID,
    step_id: UUID,
    current_user: CurrentUser,
    payload: RunbookStepUpdate,
) -> RunbookStep:
    """Update a runbook step's status and notes"""
    logger.info(f"Updating step {step_id} for runbook {runbook_id}")

    # Verify the step exists and belongs to the correct runbook/incident
    step = (
        db.query(RunbookStep)
        .join(Runbook)
        .filter(
            RunbookStep.id == step_id,
            RunbookStep.runbook_id == runbook_id,
            Runbook.incident_id == incident_id,
        )
        .first()
    )

    if not step:
        logger.warning(
            f"Step not found: {step_id} for runbook {runbook_id} in incident {incident_id}"
        )
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND, detail="Runbook step not found"
        )

    try:
        # Update fields from payload
        update_data = payload.model_dump(exclude_unset=True)

        for field, value in update_data.items():
            setattr(step, field, value)

        # Set execution details if status is being updated to non-pending
        if payload.status and payload.status != StepStatusEnum.PENDING:
            setattr(step, "executed_at", datetime.now())
            setattr(step, "executed_by", current_user.get_uuid())

        db.commit()
        db.refresh(step)
        logger.info(f"Successfully updated step: {step_id}")
        return step
    except Exception as e:
        logger.error(f"Failed to update step {step_id}: {str(e)}")
        raise


def delete_runbook_step(
    db: Session, incident_id: UUID, runbook_id: UUID, step_id: UUID
):
    logger.info(
        f"Deleting step {step_id} for runbook {runbook_id} in incident {incident_id}"
    )
    step = (
        db.query(RunbookStep)
        .join(Runbook)
        .filter(
            RunbookStep.id == step_id,
            RunbookStep.runbook_id == runbook_id,
            Runbook.incident_id == incident_id,
        )
        .first()
    )
    if not step:
        logger.warning(
            f"Step not found: {step_id} for runbook {runbook_id} in incident {incident_id}"
        )
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND, detail="Runbook step not found"
        )
    try:
        db.delete(step)
        db.commit()
        logger.info(f"Successfully deleted step: {step_id}")

        steps = (
            db.query(RunbookStep)
            .filter(RunbookStep.runbook_id == runbook_id)
            .order_by(RunbookStep.step_order)
            .all()
        )
        for idx, s in enumerate(steps, start=1):
            s.step_order = idx
        db.commit()
        logger.info(f"Resequenced step_order for runbook {runbook_id}")
    except Exception as e:
        db.rollback()
        logger.error(f"Failed to delete step {step_id}: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to delete runbook step: {str(e)}",
        )
