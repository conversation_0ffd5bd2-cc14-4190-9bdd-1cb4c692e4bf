from typing import Any, Dict, Optional
from uuid import UUID

from database.core import DbSession
from entities.incident import Incident, IncidentDetail


def get_incident_by_id(db: DbSession, incident_id: UUID) -> Incident | None:
    """Retrieve an incident by its UUID."""
    return db.query(Incident).filter(Incident.id == incident_id).first()


def get_incident_by_number(db: DbSession, incident_number: str) -> Incident | None:
    """Retrieve an incident by its incident number."""
    return (
        db.query(Incident).filter(Incident.incident_number == incident_number).first()
    )


def get_incident_details(db: DbSession, incident_id: UUID) -> IncidentDetail | None:
    """Retrieve incident details by incident UUID."""
    return (
        db.query(IncidentDetail)
        .filter(IncidentDetail.incident_id == incident_id)
        .first()
    )


def get_incident_ai_analysis(
    db: DbSession, incident_id: UUID
) -> Optional[Dict[str, Any]]:
    """Get root cause analysis data from incident details."""
    incident_detail = get_incident_details(db, incident_id)

    if not incident_detail:
        return None

    # Check if root cause analysis data exists
    if not incident_detail.root_cause:
        return None

    return {
        "root_cause": incident_detail.root_cause,
        "immediate_action": incident_detail.immediate_action,
        "impact_forecast": incident_detail.impact_forecast,
        "cascading_risks": incident_detail.cascading_risks,
    }


def save_incident_ai_analysis(
    db: DbSession, incident_id: UUID, analysis_data: Dict[str, Any]
) -> IncidentDetail:
    """Save AI analysis data efficiently."""
    incident_detail = get_incident_details(db, incident_id)

    if not incident_detail:
        incident_detail = IncidentDetail(incident_id=incident_id)
        db.add(incident_detail)

    # Update AI analysis fields
    incident_detail.root_cause = analysis_data.get("root_cause", "")
    incident_detail.immediate_action = analysis_data.get("immediate_action", "")
    incident_detail.impact_forecast = analysis_data.get("impact_forecast", "")
    incident_detail.cascading_risks = analysis_data.get("cascading_risks", "")

    db.commit()
    db.refresh(incident_detail)
    return incident_detail


def save_incident_summary(db: DbSession, incident_id: UUID, summary: str) -> Incident:
    """Save incident summary to summary field efficiently."""
    incident = get_incident_by_id(db, incident_id)

    if not incident:
        raise ValueError(f"Incident with id {incident_id} not found")

    incident.summary = summary

    db.commit()
    db.refresh(incident)
    return incident
