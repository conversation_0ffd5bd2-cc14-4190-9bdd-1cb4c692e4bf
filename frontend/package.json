{"name": "incident-management-ui", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "format": "prettier . --write", "lint": "eslint .", "preview": "vite preview", "test": "jest", "test:watch": "jest --watch", "prepare": "cd .. && husky frontend/.husky"}, "dependencies": {"@hookform/resolvers": "^5.1.1", "@mantine/charts": "^8.1.3", "@mantine/core": "^8.1.3", "@mantine/dates": "^8.1.3", "@mantine/hooks": "^8.1.3", "@tailwindcss/vite": "^4.1.11", "@tanstack/react-query": "^5.81.5", "dayjs": "^1.11.13", "lucide-react": "^0.525.0", "react": "^19.1.0", "react-dom": "^19.1.0", "react-hook-form": "^7.60.0", "react-markdown": "^10.1.0", "react-router": "^7.6.3", "react-toastify": "^11.0.5", "recharts": "^2.15.4", "rehype-highlight": "^7.0.2", "rehype-raw": "^7.0.0", "rehype-sanitize": "^6.0.0", "remark-breaks": "^4.0.0", "remark-gfm": "^4.0.1", "tailwindcss": "^4.1.11", "zod": "^3.25.75"}, "devDependencies": {"@eslint/js": "^9.30.1", "@tanstack/react-query-devtools": "^5.81.5", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.6.1", "@types/jest": "^30.0.0", "@types/node": "^24.0.10", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "@vitejs/plugin-react": "^4.6.0", "eslint": "^9.30.1", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "globals": "^16.3.0", "husky": "^9.1.7", "jest": "^30.0.4", "jest-environment-jsdom": "^30.0.4", "prettier": "3.6.2", "ts-jest": "^29.4.0", "ts-node": "^10.9.2", "typescript": "~5.8.3", "typescript-eslint": "^8.36.0", "vite": "^7.0.2"}, "packageManager": "pnpm@9.15.2+sha512.93e57b0126f0df74ce6bff29680394c0ba54ec47246b9cf321f0121d8d9bb03f750a705f24edc3c1180853afd7c2c3b94196d0a3d53d3e069d9e2793ef11f321"}